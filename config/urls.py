from django.conf import settings
from django.urls import path, include
from django.conf.urls.static import static
from django.contrib import admin
from rest_framework_simplejwt.views import TokenRefreshView, TokenVerifyView
from rest_framework.views import APIView
from rest_framework.response import Response


from apps.users.custom_jwt import (
    MyTokenObtainPairView,
    DashboardTokenObtainPairView,
    CourierTokenObtainPairView
)
from drf_yasg import openapi
from drf_yasg.views import get_schema_view
from apps.bot.views.webhook import webhook, courier_webhook

schema_view = get_schema_view(
    openapi.Info(
        title="obihayot API",
        default_version="v1.0.0",
        description="Obihayot crm project",
    ),
    public=True,
)


class PingView(APIView):
    def get(self, request):
        return Response({"detail": "Pong!"})


urlpatterns = [
    path("admin/", admin.site.urls),
    # path("api/v1/", include("apps.users.urls")),
    path("api/v1/users/", include("apps.users.urls")),
    path('chaining/', include('smart_selects.urls')),
    path("api-auth/", include("rest_framework.urls", namespace="rest_framework")),
    # JWT
    path("api/v1/token/", MyTokenObtainPairView.as_view(), name="token_obtain_pair"),
    path(
        "api/v1/dashboard/login/",
        DashboardTokenObtainPairView.as_view(),
        name="dashboard_token_obtain_pair"
    ),
    path("api/v1/vendors/", include("apps.vendors.urls")),
    path("api/v1/token/refresh/", TokenRefreshView.as_view(), name="token_refresh"),
    path("api/v1/token/verify/", TokenVerifyView.as_view(), name="token_verify"),
    # Users
    path("api/v1/users/customers/", include("apps.users.customers.urls")),
    path("api/v1/users/vendors/", include("apps.users.vendors.urls")),
    path("api/v1/", include("apps.bot.urls")),
    path("api/v1/helpers/", include("apps.helpers.urls")),
    path("api/v1/consultations/", include("apps.consultation.urls")),
    path("api/v1/products/", include("apps.products.urls")),
    path("api/v1/orders/", include("apps.orders.urls")),
    path("api/v1/couriers/", include("apps.users.couriers.urls")),
    path("api/v1/notifications/", include("apps.notifications.urls")),  # Added notifications URLs
    path("telegram/webhook/", webhook, name="telegram-webhook"),
    path("telegram/courier-webhook/", courier_webhook, name="telegram-courier-webhook"),
    # Swagger
    path("swagger/", schema_view.with_ui("swagger", cache_timeout=0), name="swagger"),
    path("redoc/", schema_view.with_ui("redoc", cache_timeout=0), name="redoc"),
    path("ping/", PingView.as_view(), name="ping"),
    path(
        "api/v1/couriers/login/",
        CourierTokenObtainPairView.as_view(),
        name="courier_token_obtain_pair"
    ),
]

urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
