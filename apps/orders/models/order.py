"""
Order models module.
"""
import re

from django.db import models
from django.utils import timezone
from django.core.validators import MinValueValidator
from django.core.exceptions import ValidationError

from apps.users.models import Customer, Vendor, Courier
from apps.users.vendors.models import Vendor<PERSON>lient, VendorStats
from apps.products.models import Product
from apps.helpers.models import Region, District
from apps.users.validators import is_valid_uzb_phone_number
from core.base_models import BaseModel


class OrderStatus(models.TextChoices):
    """
    Choices for order status.
    """
    PENDING = 'pending'
    ACCEPTED = 'accepted'
    DELIVERING = 'delivering'
    FINISHED = 'finished'
    CANCELLED = 'cancelled'
    REJECTED = 'rejected'


ACTIVE_STATUSES = {
    OrderStatus.PENDING,
    OrderStatus.ACCEPTED,
    OrderStatus.DELIVERING,
}

ARCHIVED_STATUSES = {
    OrderStatus.FINISHED,
    OrderStatus.CANCELLED,
    OrderStatus.REJECTED,
}


def get_status_group(group_name):
    """
    Return the set of statuses for the given group name: 'active' or 'archived'.
    """
    if group_name == 'active':
        return ACTIVE_STATUSES
    elif group_name == 'archived':
        return ARCHIVED_STATUSES
    else:
        raise ValueError(f"Invalid status group: {group_name}")

# Example usage:
# orders = Order.objects.filter(status__in=get_status_group('active'))


class Order(BaseModel):
    """
    Model representing customer orders
    """
    # Related models
    customer = models.ForeignKey(
        Customer, on_delete=models.SET_NULL, null=True, blank=True
    )
    vendor = models.ForeignKey(
        Vendor, on_delete=models.SET_NULL, null=True, blank=True
    )
    product = models.ForeignKey(
        Product, on_delete=models.SET_NULL, null=True
    )
    courier = models.ForeignKey(
        Courier, on_delete=models.SET_NULL, null=True, blank=True
    )
    region = models.ForeignKey(Region, on_delete=models.SET_NULL, null=True)
    district = models.ForeignKey(District, on_delete=models.SET_NULL, null=True)

    # Order details
    fullname = models.CharField(max_length=100, null=True, blank=True)
    quantity = models.IntegerField(validators=[MinValueValidator(1)])
    status = models.CharField(
        max_length=100,
        choices=OrderStatus.choices,
        default=OrderStatus.PENDING
    )

    # Bottle tracking
    returned_bottles = models.IntegerField(default=0, null=True, blank=True)
    old_client_return_bottles = models.IntegerField(
        default=0,
        null=True,
        blank=True,
        help_text="Bottles returned by old clients during order creation"
    )

    # Location and contact info
    address = models.CharField(max_length=255)
    longitude = models.FloatField(null=True, blank=True)
    latitude = models.FloatField(null=True, blank=True)
    phone_number = models.CharField(
        max_length=15,
        validators=[is_valid_uzb_phone_number],
        null=True,
        blank=True
    )
    additional_number = models.CharField(
        max_length=15,
        validators=[is_valid_uzb_phone_number],
        null=True,
        blank=True
    )

    # Additional information
    passport = models.CharField(max_length=128, null=True, blank=True)
    deliverable_time = models.CharField(max_length=255, null=True, blank=True)
    is_first_order = models.BooleanField(default=False)

    # Timestamps
    delivered_at = models.DateTimeField(null=True, blank=True)
    accept_time = models.DateTimeField(null=True, blank=True)

    class Meta:
        """Meta options for Order model."""
        verbose_name = 'order'
        verbose_name_plural = 'Orders'

    def __str__(self):
        """
        Return a string representation of the order.
        """
        customer_name = getattr(self.customer, 'fullname', None)
        product_title = getattr(self.product, 'title', 'Unknown Product')
        vendor_name = getattr(self.vendor, 'name', None)

        if customer_name:
            return f"{customer_name} - {product_title}"
        elif vendor_name:
            return f"{vendor_name} - {product_title}"
        return f"Order - {product_title}"

    @property
    def total_price(self):
        """
        Calculate the total price of the order.
        """
        return self.quantity * self.product.price

    def clean(self):
        """
        Validate model data before saving.
        """
        super().clean()
        if self.deliverable_time:
            pattern = r'^\d{2}:\d{2} - \d{2}:\d{2}$'
            if not re.match(pattern, self.deliverable_time):
                raise ValidationError(
                    {'deliverable_time': 'Format must be HH:MM - HH:MM'}
                )

    def save(self, *args, **kwargs):
        """
        Override save method to handle business logic before saving.
        """
        # Set vendor from product if not specified
        if not self.vendor and self.product:
            self.vendor = self.product.vendor

        # Check if this is the customer's first order
        if self._state.adding and self.customer:
            self.is_first_order = not Order.objects.filter(customer=self.customer).exists()

        # Ensure the order is created with status pending only on creation
        if self._state.adding:
            self.status = OrderStatus.PENDING
            # Handle old client return bottles during order creation
            if self.old_client_return_bottles and self.old_client_return_bottles > 0:
                self._handle_old_client_return_bottles()

        # Handle order completion and bottle returns
        if self.status == OrderStatus.FINISHED:
            if not self.delivered_at:
                self.delivered_at = timezone.now()
            self._process_vendor_client(update_bottles=True)
        # Handle order accepted status to update vendor client
        elif self.status == OrderStatus.ACCEPTED:
            # Check if vendor client exists, if not, process vendor client
            from apps.users.vendors.models import VendorClient
            if not VendorClient.objects.filter(vendor=self.vendor, customer=self.customer).exists():
                self._process_vendor_client(update_bottles=True)

        super().save(*args, **kwargs)

    def _process_vendor_client(self, update_bottles=False):
        """
        Process vendor client operations.
        """
        # Prepare arguments for get_or_create
        vendor_client_kwargs = {'vendor': self.vendor}
        if self.customer:
            vendor_client_kwargs['customer'] = self.customer
        else:
            vendor_client_kwargs['phone_number'] = self.phone_number

        # Get or create the vendor client
        vendor_client, _ = VendorClient.objects.get_or_create(**vendor_client_kwargs)

        # Update client information if needed
        if update_bottles and self.status == OrderStatus.FINISHED:
            vendor_client.address = self.address
            vendor_client.region = self.region
            vendor_client.district = self.district
            vendor_client.additional_number = self.additional_number
            vendor_client.orders_count += 1
            

            # Calculate return bottles only after order is finished
            # Adjusted to handle returned_bottles = 0 case
            if self.returned_bottles is not None:
                vendor_client.return_bottles += (self.quantity - self.returned_bottles)

        vendor_client.save()

    def _handle_old_client_return_bottles(self):
        """
        Handle return bottles from old clients during order creation.
        Updates vendor stats to reflect bottles that old clients already have.
        """
        if not self.vendor or not self.old_client_return_bottles:
            return

        # Get or create vendor stats
        from apps.users.vendors.models import VendorStats
        vendor_stats, _ = VendorStats.objects.get_or_create(
            vendor=self.vendor,
            defaults={'free_bottles': 0, 'busy_bottles': 0}
        )

        # Update vendor stats: old client has bottles
        # free_bottles decrease (bottles are not free, they're with the client)
        # busy_bottles increase (bottles are busy with the client)
        vendor_stats.free_bottles = max(0, vendor_stats.free_bottles - self.old_client_return_bottles)
        vendor_stats.busy_bottles += self.old_client_return_bottles
        vendor_stats.save()

    # Status transition methods
    def set_status_pending(self):
        """
        Change order status to PENDING.
        """
        self.status = OrderStatus.PENDING
        self.save(update_fields=['status'])
        return self

    def set_status_accepted(self):
        """
        Change order status to ACCEPTED.
        """
        self.status = OrderStatus.ACCEPTED
        self.accept_time = timezone.now()
        self.save(update_fields=['status', 'accept_time'])
        return self

    def set_status_delivering(self):
        """
        Change order status to DELIVERING.
        """
        self.status = OrderStatus.DELIVERING
        self.save(update_fields=['status'])
        return self

    def set_status_finished(self, delivered_quantity=None, returned_bottles=0):
        """
        Change order status to FINISHED with optional parameter updates.
        """

        
        self.status = OrderStatus.FINISHED

        # Update quantities if provided
        if delivered_quantity is not None:
            self.quantity = delivered_quantity

        if returned_bottles is not None:
            self.returned_bottles = returned_bottles

        # Process vendor client and update bottles
        self._process_vendor_client(update_bottles=True)

        # Update vendor statistics with new bottle calculation algorithm
        vendor_stats = VendorStats.objects.get(vendor=self.vendor)

        dq = delivered_quantity if delivered_quantity is not None else self.quantity
        rb = returned_bottles if returned_bottles is not None else 0

        # Apply new bottle calculation algorithm
        vendor_stats.free_bottles = max(0, vendor_stats.free_bottles - dq + rb)
        vendor_stats.busy_bottles = max(0, vendor_stats.busy_bottles + dq - rb)

        vendor_stats.save()

        super().save()
        return self

    def change_status(self, new_status):
        """
        Change order status to the provided status.
        """
        if new_status not in OrderStatus.values:
            raise ValueError(f"Invalid status: {new_status}")

        status_methods = {
            OrderStatus.PENDING: self.set_status_pending,
            OrderStatus.ACCEPTED: self.set_status_accepted,
            OrderStatus.DELIVERING: self.set_status_delivering,
            OrderStatus.FINISHED: self.set_status_finished,
        }

        return status_methods[new_status]()
